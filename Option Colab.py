# TradingAgents: Multi-Agent LLM Options Trading Framework
#
# Version: 2.0
# Description: This script implements an enhanced multi-agent LLM system for simulating SPY ETF options trading decisions.
# It features specialized analyst roles, including a quantitative-style option pricing analyst, and incorporates
# portfolio state awareness into the final trading decision.
#
# Author: AI Systems Architect & Quantitative Developer
# Date: 2024-05-17
#

import os
import re
import json
import math
import logging
import time
import requests
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import pandas as pd
import numpy as np
import yfinance as yf
import pandas_datareader.data as web
from fredapi import Fred

# OpenRouter API will be accessed via requests

# Pandas configuration for Sperandeo functions
pd.options.mode.chained_assignment = None  # silence chained assignment warnings
pd.set_option('future.no_silent_downcasting', True)  # suppress downcasting warnings

# --- CONFIGURATION ---

# API Keys
OPENROUTER_API_KEY = "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"
FRED_API_KEY = "dac90176cd11cbf8281068404bc30b7a"

ALPHAVANTAGE_API_KEY = os.getenv("ALPHAVANTAGE_API_KEY", "LBSPY17KXDHGHCPT")

# LLM Configuration - Strategic model selection as per paper
DEEP_THINKING_MODEL = "x-ai/grok-4-fast:free"  # For analysis tasks
QUICK_THINKING_MODEL = "x-ai/grok-4-fast:free"  # Would use gpt-4o-mini in production
DEBATE_MODEL = "x-ai/grok-4-fast:free"

# Simulation Parameters
ANALYSIS_WINDOW_DAYS = 126
HV_WINDOW = 21
DTE_CHOICES = [5, 8, 15, 22, 29, 36, 43, 57, 64, 85, 97, 113, 127]
DEBATE_ROUNDS = 3  # Number of debate rounds

# 4. Data Tickers and Series
MARKET_TICKERS = ["SPY", "^VIX", "^IRX"] # SPY ETF, VIX Index, 13-week T-bill (^IRX as risk-free rate)
FRED_SERIES = {
    "T10Y2Y": "10-Year Minus 2-Year Treasury Yield Spread",
    "USEPUINDXD": "Economic Policy Uncertainty Index for United States",
    "ADPWNUSNERSA": "Total Nonfarm Private Payroll Employment",
    "EMVOVERALLEMV": "Equity Market Volatility Tracker: Overall",
    "ICSA": "Initial Claims",
    "UMCSENT": "University of Michigan: Consumer Sentiment"
}

def normalize_fred_indicators(
    api_key: str,
    lookback_periods: int = 24,  # months for historical context
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Fetch FRED API data and normalize indicators to -1 to 1 scale based on 3-month average.
    Handles different data frequencies (daily, weekly, monthly).

    Parameters:
    -----------
    api_key : str
        FRED API key
    lookback_periods : int
        Number of months to use for normalization context (default: 24)
    start_date : str, optional
        Start date for data fetch (format: 'YYYY-MM-DD')
    end_date : str, optional
        End date for data fetch (format: 'YYYY-MM-DD')

    Returns:
    --------
    Tuple[pd.DataFrame, pd.DataFrame]
        - DataFrame with daily aligned data and 3-month averages
        - DataFrame with normalized indicators
    """

    # Initialize FRED API
    fred = Fred(api_key=api_key)

    # Define indicators with their CORRECT properties and frequencies
    indicators = {
        "T10Y2Y": {
            "name": "10-Year Minus 2-Year Treasury Yield Spread",
            "inverse": False,  # Higher spread is generally positive
            "frequency": "daily"
        },
        "USEPUINDXD": {
            "name": "Economic Policy Uncertainty Index",
            "inverse": True,  # Higher uncertainty is negative
            "frequency": "daily"
        },
        "ADPWNUSNERSA": {
            "name": "Total Nonfarm Private Payroll Employment",
            "inverse": False,  # Higher employment is positive
            "frequency": "monthly",
            "transform": "pct_change"  # Use percent change for employment levels
        },
        "EMVOVERALLEMV": {
            "name": "Equity Market Volatility Tracker",
            "inverse": True,  # Higher volatility is negative
            "frequency": "monthly"  # CORRECTED: This is monthly data
        },
        "ICSA": {
            "name": "Initial Claims",
            "inverse": True,  # Higher claims is negative
            "frequency": "weekly"
        },
        "UMCSENT": {
            "name": "Consumer Sentiment",
            "inverse": False,  # Higher sentiment is positive
            "frequency": "monthly"
        }
    }

    # Set date range with extra buffer for rolling calculations
    if end_date is None:
        end_date = datetime.now().strftime('%Y-%m-%d')
    if start_date is None:
        # Fetch extra data for rolling calculations and normalization
        start_date = (datetime.now() - timedelta(days=(lookback_periods + 6) * 30)).strftime('%Y-%m-%d')

    # Store all series with their original frequencies
    raw_data = {}
    processed_data = {}

    print("Fetching and processing FRED data...")
    print("-" * 50)

    for symbol, props in indicators.items():
        try:
            # Fetch data from FRED
            series = fred.get_series(symbol, start_date, end_date)

            if series is None or len(series) == 0:
                print(f"Warning: No data retrieved for {symbol}")
                continue

            # Convert to DataFrame
            df = pd.DataFrame(series, columns=[symbol])

            # Store raw data
            raw_data[symbol] = df.copy()

            # Process based on data frequency and characteristics
            processed = process_series_by_frequency(
                df[symbol],
                symbol,
                props,
                target_freq='D'  # Resample to daily for consistent 3-month calculations
            )

            processed_data[symbol] = processed

            # Get actual data frequency for reporting
            actual_freq = detect_actual_frequency(df[symbol])
            print(f"✓ {symbol}: {props['frequency']} data (detected: {actual_freq}), {len(df)} observations")

        except Exception as e:
            print(f"✗ Error processing {symbol}: {str(e)}")
            continue

    if not processed_data:
        raise ValueError("No data could be fetched from FRED API")

    # Combine all processed series into a single DataFrame
    combined_df = pd.DataFrame(processed_data)

    # Forward fill to handle weekends/holidays for daily data
    # But limit the forward fill to avoid excessive propagation
    combined_df = combined_df.fillna(method='ffill', limit=5)

    # Calculate 3-month (90-day) moving averages
    ma_df = pd.DataFrame(index=combined_df.index)
    for symbol in combined_df.columns:
        ma_df[f'{symbol}_3MA'] = combined_df[symbol].rolling(window=90, min_periods=30).mean()

    # Normalize each indicator
    normalized_df = pd.DataFrame(index=combined_df.index)

    for symbol, props in indicators.items():
        if f'{symbol}_3MA' in ma_df.columns:
            normalized = normalize_with_adaptive_zscore(
                ma_df[f'{symbol}_3MA'],
                lookback_days=lookback_periods * 30,
                inverse=props['inverse']
            )
            normalized_df[f'{symbol}_normalized'] = normalized

    # Calculate composite score (weighted by data availability)
    if len(normalized_df.columns) > 0:
        # Calculate composite with handling for missing values
        normalized_df['composite_score'] = calculate_weighted_composite(normalized_df)

    # Combine original and normalized data
    result_df = pd.concat([combined_df, ma_df, normalized_df], axis=1)

    print(f"\nProcessing complete. Data shape: {result_df.shape}")

    return result_df, normalized_df

def detect_actual_frequency(series: pd.Series) -> str:
    """
    Detect the actual frequency of a time series.

    Parameters:
    -----------
    series : pd.Series
        Input series

    Returns:
    --------
    str
        Detected frequency ('daily', 'weekly', 'monthly', or 'irregular')
    """
    if len(series) < 2:
        return "unknown"

    # Calculate time differences
    time_diffs = pd.Series(series.index[1:]) - pd.Series(series.index[:-1])
    median_diff = time_diffs.median()

    if median_diff <= pd.Timedelta(days=2):
        return "daily"
    elif median_diff <= pd.Timedelta(days=10):
        return "weekly"
    elif median_diff <= pd.Timedelta(days=35):
        return "monthly"
    else:
        return "irregular"


def process_series_by_frequency(
    series: pd.Series,
    symbol: str,
    props: dict,
    target_freq: str = 'D'
) -> pd.Series:
    """
    Process series based on its frequency and characteristics.

    Parameters:
    -----------
    series : pd.Series
        Input series
    symbol : str
        Symbol name
    props : dict
        Properties including frequency and transformation
    target_freq : str
        Target frequency for resampling ('D' for daily)

    Returns:
    --------
    pd.Series
        Processed series at target frequency
    """
    # Handle transformations for level series
    if props.get('transform') == 'pct_change':
        # For employment levels, use month-over-month percent change
        if props['frequency'] == 'monthly':
            series = series.pct_change() * 100  # Convert to percentage
        else:
            series = series.pct_change(periods=21) * 100  # 21 trading days ≈ 1 month

    # Resample to target frequency based on original frequency
    if props['frequency'] == 'daily':
        # Already daily, just ensure proper index
        processed = series.resample('D').last().fillna(method='ffill', limit=5)

    elif props['frequency'] == 'weekly':
        # Weekly data - interpolate to daily with limits
        # Use linear interpolation but limit to avoid over-extrapolation
        processed = series.resample('D').interpolate(method='linear', limit=6)

    elif props['frequency'] == 'monthly':
        # Monthly data - use more conservative interpolation
        # First forward fill, then interpolate for smoother transitions
        resampled = series.resample('D').fillna(method='ffill')
        # Apply smooth interpolation between monthly points
        processed = resampled.interpolate(method='cubic', limit_direction='forward')

    else:
        # Unknown frequency, attempt to detect and process
        processed = auto_resample_to_daily(series)

    return processed


def auto_resample_to_daily(series: pd.Series) -> pd.Series:
    """
    Automatically detect frequency and resample to daily.

    Parameters:
    -----------
    series : pd.Series
        Input series with unknown frequency

    Returns:
    --------
    pd.Series
        Daily resampled series
    """
    freq = detect_actual_frequency(series)

    if freq == "daily":
        return series.resample('D').last().fillna(method='ffill', limit=5)
    elif freq == "weekly":
        return series.resample('D').interpolate(method='linear', limit=6)
    elif freq == "monthly":
        resampled = series.resample('D').fillna(method='ffill')
        return resampled.interpolate(method='cubic', limit_direction='forward')
    else:
        # For irregular data, use simple forward fill
        return series.resample('D').fillna(method='ffill')


def calculate_weighted_composite(normalized_df: pd.DataFrame) -> pd.Series:
    """
    Calculate weighted composite score giving more weight to indicators with more recent data.

    Parameters:
    -----------
    normalized_df : pd.DataFrame
        DataFrame with normalized indicators

    Returns:
    --------
    pd.Series
        Weighted composite score
    """
    # Get only normalized columns (exclude composite_score if it exists)
    norm_cols = [col for col in normalized_df.columns if '_normalized' in col]

    if not norm_cols:
        return pd.Series(index=normalized_df.index)

    # Calculate weights based on data recency and availability
    weights = {}
    for col in norm_cols:
        # Check how recent the data is
        last_valid = normalized_df[col].last_valid_index()
        if last_valid is not None:
            days_old = (normalized_df.index[-1] - last_valid).days
            # Weight decreases with age of data
            weight = max(0.1, 1.0 - (days_old / 30))  # Reduce weight by ~3% per day old
            weights[col] = weight
        else:
            weights[col] = 0

    # Normalize weights
    total_weight = sum(weights.values())
    if total_weight > 0:
        weights = {k: v/total_weight for k, v in weights.items()}

    # Calculate weighted average
    composite = pd.Series(index=normalized_df.index, dtype=float)
    for idx in normalized_df.index:
        weighted_sum = 0
        weight_sum = 0
        for col in norm_cols:
            if pd.notna(normalized_df.loc[idx, col]):
                weighted_sum += normalized_df.loc[idx, col] * weights.get(col, 0)
                weight_sum += weights.get(col, 0)

        if weight_sum > 0:
            composite[idx] = weighted_sum / weight_sum
        else:
            composite[idx] = np.nan

    return composite


def normalize_with_adaptive_zscore(
    series: pd.Series,
    lookback_days: int = 720,  # 24 months
    inverse: bool = False,
    z_bound: float = 2.5
) -> pd.Series:
    """
    Normalize series to -1 to 1 using adaptive z-score with rolling window.

    Parameters:
    -----------
    series : pd.Series
        Input series to normalize (already as 3-month average)
    lookback_days : int
        Number of days for rolling statistics
    inverse : bool
        If True, multiply by -1 (for indicators where higher is worse)
    z_bound : float
        Z-score bounds for clipping (default: 2.5)

    Returns:
    --------
    pd.Series
        Normalized series between -1 and 1
    """
    # Skip if series is all NaN
    if series.isna().all():
        return series

    # Calculate rolling statistics with expanding window for initial period
    min_periods = min(90, len(series.dropna()) // 4)  # At least 90 days or 1/4 of data

    rolling_mean = series.rolling(
        window=lookback_days,
        min_periods=min_periods
    ).mean()

    rolling_std = series.rolling(
        window=lookback_days,
        min_periods=min_periods
    ).std()

    # Use expanding window for initial period
    expanding_mean = series.expanding(min_periods=min_periods).mean()
    expanding_std = series.expanding(min_periods=min_periods).std()

    # Combine rolling and expanding (use expanding for initial period)
    rolling_mean = rolling_mean.fillna(expanding_mean)
    rolling_std = rolling_std.fillna(expanding_std)

    # Avoid division by zero
    rolling_std = rolling_std.replace(0, np.nan)

    # Calculate z-score
    z_score = (series - rolling_mean) / rolling_std

    # Clip z-scores to bounds
    z_score = np.clip(z_score, -z_bound, z_bound)

    # Scale to -1 to 1
    normalized = z_score / z_bound

    # Inverse if needed (for negative indicators)
    if inverse:
        normalized = -normalized

    return normalized


def get_latest_signals(
    df: pd.DataFrame,
    lookback_days: int = 30
) -> Dict[str, any]:
    """
    Extract the latest normalized signals and trends.

    Parameters:
    -----------
    df : pd.DataFrame
        DataFrame with normalized indicators
    lookback_days : int
        Days to look back for trend calculation

    Returns:
    --------
    dict
        Dictionary with latest signals and trends
    """
    signals = {
        'date': None,
        'values': {},
        'trends': {},
        'strength': {},
        'data_age': {}  # How old is the last data point for each indicator
    }

    # Get the last valid row
    valid_data = df.dropna(how='all')
    if len(valid_data) == 0:
        return signals

    latest_idx = valid_data.index[-1]
    signals['date'] = latest_idx.strftime('%Y-%m-%d')

    # Extract normalized columns
    normalized_cols = [col for col in df.columns if '_normalized' in col or col == 'composite_score']

    for col in normalized_cols:
        # Find last valid value for this indicator
        last_valid_idx = df[col].last_valid_index()

        if last_valid_idx is not None:
            # Current value
            current_value = df.loc[last_valid_idx, col]
            signals['values'][col] = round(current_value, 3)

            # Data age
            days_old = (latest_idx - last_valid_idx).days
            signals['data_age'][col] = days_old

            # Calculate trend (change over lookback period)
            past_idx = last_valid_idx - pd.Timedelta(days=lookback_days)
            if past_idx in df.index and pd.notna(df.loc[past_idx, col]):
                past_value = df.loc[past_idx, col]
                trend = current_value - past_value
                signals['trends'][col] = round(trend, 3)

            # Determine signal strength
            abs_value = abs(current_value)
            if abs_value > 0.75:
                strength = "Very Strong"
            elif abs_value > 0.5:
                strength = "Strong"
            elif abs_value > 0.25:
                strength = "Moderate"
            else:
                strength = "Weak"

            signals['strength'][col] = strength

    return signals


def print_summary_table(signals: Dict[str, any]):
    """
    Print a formatted summary table of the latest signals.

    Parameters:
    -----------
    signals : dict
        Dictionary with signals from get_latest_signals
    """
    print("\n" + "=" * 80)
    print(f"Economic Indicators Dashboard (as of {signals['date']})")
    print("=" * 80)
    print("\nScale: -1.000 (Most Negative) to +1.000 (Most Positive)")
    print("-" * 80)
    print(f"{'Indicator':<25} {'Value':>8} {'Trend':>8} {'Strength':<15} {'Age (days)':<10}")
    print("-" * 80)

    # Sort indicators by absolute value (most significant first)
    sorted_indicators = sorted(signals['values'].items(),
                              key=lambda x: abs(x[1]),
                              reverse=True)

    for indicator, value in sorted_indicators:
        # Format indicator name
        name = indicator.replace('_normalized', '').replace('_', ' ').upper()
        if name == 'COMPOSITE SCORE':
            print("-" * 80)  # Separator before composite

        # Get trend
        trend = signals['trends'].get(indicator, 0)
        trend_str = f"{trend:+.3f}" if trend != 0 else "  -   "

        # Get strength
        strength = signals['strength'].get(indicator, "Unknown")

        # Get data age
        age = signals['data_age'].get(indicator, 0)
        age_str = str(age) if age > 0 else "Current"

        # Determine visual indicator
        if value > 0.5:
            emoji = "🟢"  # Strong positive
        elif value > 0:
            emoji = "🟡"  # Weak positive
        elif value > -0.5:
            emoji = "🟠"  # Weak negative
        else:
            emoji = "🔴"  # Strong negative

        print(f"{emoji} {name:<23} {value:>+8.3f} {trend_str:>8} {strength:<15} {age_str:<10}")

    print("=" * 80)

# 5. Logging Setup
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


# --- SPERANDEO TECHNICAL ANALYSIS FUNCTIONS ---

def get_spy_market_data(ticker: str = "SPY", days: int = 126) -> pd.DataFrame:
    """
    Fetches the last N trading days of OHLCV market data for SPY.
    Enhanced version for Sperandeo analysis requiring full OHLCV data.
    """
    logging.info(f"Fetching last {days} trading days of OHLCV data for {ticker}...")
    end_date = datetime.now()
    start_date = end_date - timedelta(days=int(days * 2.2))
    data = yf.download(
        ticker,
        start=start_date.strftime("%Y-%m-%d"),
        end=end_date.strftime("%Y-%m-%d"),
        progress=False,
        auto_adjust=False,
        ignore_tz=True,
        interval="1d",
    )
    if data.empty:
        raise ValueError(f"Failed to fetch OHLCV data for {ticker}")

    # Handle MultiIndex columns if present
    if isinstance(data.columns, pd.MultiIndex):
        data.columns = data.columns.droplevel(1)

    data = data.dropna()
    data = data.tail(days).copy()
    logging.info(f"OHLCV data fetched: {len(data)} rows.")
    return data


def _local_extrema_flags(df: pd.DataFrame) -> Tuple[pd.Series, pd.Series]:
    """
    Basic local maxima/minima on a one-bar neighborhood.
    """
    is_peak = (df["High"] > df["High"].shift(1)) & (df["High"] > df["High"].shift(-1))
    is_trough = (df["Low"] < df["Low"].shift(1)) & (df["Low"] < df["Low"].shift(-1))
    return is_peak.fillna(False), is_trough.fillna(False)


def identify_trend(df: pd.DataFrame) -> pd.DataFrame:
    """
    Identifies trend regime using swing structure:
    - Uptrend: higher highs and higher lows
    - Downtrend: lower highs and lower lows
    - Else: sideways (0)
    """
    df = df.copy()
    is_peak, is_trough = _local_extrema_flags(df)

    peaks = df["High"].where(is_peak)
    troughs = df["Low"].where(is_trough)

    last_peak = peaks.ffill()
    prev_peak = peaks.ffill().shift(1)
    last_trough = troughs.ffill()
    prev_trough = troughs.ffill().shift(1)

    is_uptrend = (last_peak > prev_peak) & (last_trough > prev_trough)
    is_downtrend = (last_peak < prev_peak) & (last_trough < prev_trough)

    df["trend_state"] = 0
    df.loc[is_uptrend, "trend_state"] = 1
    df.loc[is_downtrend, "trend_state"] = -1
    return df


def quantify_consecutive_days(df: pd.DataFrame) -> pd.DataFrame:
    """
    Computes signed streak length of consecutive up/down closes.
    Positive values: up-day streak length; Negative values: down-day streak length.
    """
    df = df.copy()
    price_change = df["Close"].diff()
    sign = np.sign(price_change).fillna(0)
    grp = (sign != sign.shift()).cumsum()
    counts = sign.groupby(grp).cumcount() + 1
    df["consecutive_days"] = counts * sign
    return df


def detect_trendline_break(df: pd.DataFrame) -> pd.DataFrame:
    """
    Approximates trendline breaks using the last two swing lows (uptrend) or highs (downtrend).
    - For uptrend line: connect last two swing lows; if Close crosses below the extrapolated line -> bearish break (+1).
    - For downtrend line: connect last two swing highs; if Close crosses above the extrapolated line -> bullish break (-1).

    Output:
      trendline_break:
        +1: bearish break of uptrend line
        -1: bullish break of downtrend line
         0: none
      trendline_slope_up, trendline_slope_down: slopes for context (NaN if not defined)
    """
    df = df.copy()
    is_peak, is_trough = _local_extrema_flags(df)

    n = len(df)
    tl_break = np.zeros(n, dtype=int)
    slope_up = np.full(n, np.nan)
    slope_down = np.full(n, np.nan)

    lows: List[Tuple[int, float]] = []
    highs: List[Tuple[int, float]] = []

    last_up_cross = False
    last_down_cross = False

    closes = df["Close"].values
    lows_arr = df["Low"].values
    highs_arr = df["High"].values

    for i in range(n):
        if is_trough.iloc[i]:
            lows.append((i, lows_arr[i]))
            if len(lows) > 2:
                lows.pop(0)
        if is_peak.iloc[i]:
            highs.append((i, highs_arr[i]))
            if len(highs) > 2:
                highs.pop(0)

        # Uptrend line from two recent swing lows
        if len(lows) == 2:
            (x1, y1), (x2, y2) = lows[0], lows[1]
            if x2 != x1:
                m_up = (y2 - y1) / (x2 - x1)
                slope_up[i] = m_up
                y_line = y1 + m_up * (i - x1)
                crossed = closes[i] < y_line
                if crossed and not last_up_cross and i >= x2:
                    tl_break[i] = 1  # bearish break
                last_up_cross = crossed

        # Downtrend line from two recent swing highs
        if len(highs) == 2:
            (x1h, y1h), (x2h, y2h) = highs[0], highs[1]
            if x2h != x1h:
                m_dn = (y2h - y1h) / (x2h - x1h)
                slope_down[i] = m_dn
                y_line_dn = y1h + m_dn * (i - x1h)
                crossed_dn = closes[i] > y_line_dn
                if crossed_dn and not last_down_cross and i >= x2h:
                    tl_break[i] = -1  # bullish break
                last_down_cross = crossed_dn

    df["trendline_break"] = tl_break
    df["trendline_slope_up"] = slope_up
    df["trendline_slope_down"] = slope_down
    return df


def apply_123_rule(df: pd.DataFrame, use_trendline_condition: bool = True) -> pd.DataFrame:
    """
    Applies Sperandeo's 1-2-3 Rule, tracking "armed" and "triggered" states.

    1-2-3 Top:
      1) Break of uptrend line (if use_trendline_condition=True, require trendline_break=+1)
      2) Lower high forms
      3) Price breaks below last swing low -> trigger

    1-2-3 Bottom: symmetric.

    Output:
      123_reversal_state:
        +1.0: Top Triggered
        +0.5: Top Armed (lower high formed after condition 1)
        -1.0: Bottom Triggered
        -0.5: Bottom Armed (higher low formed after condition 1)
    """
    df = df.copy()
    is_peak, is_trough = _local_extrema_flags(df)

    peak_vals = df["High"].where(is_peak)
    trough_vals = df["Low"].where(is_trough)

    last_peak = peak_vals.ffill()
    last_trough = trough_vals.ffill()

    # 2) Lower high formed? (vs previous peak)
    prev_peak_at_peaks = peak_vals.shift(1)
    lower_high_now = is_peak & (df["High"] < prev_peak_at_peaks)

    # 2) Higher low formed? (vs previous trough)
    prev_trough_at_troughs = trough_vals.shift(1)
    higher_low_now = is_trough & (df["Low"] > prev_trough_at_troughs)

    # Optional: enforce Condition 1 via trendline breaks (from detect_trendline_break)
    if use_trendline_condition and "trendline_break" in df.columns:
        cond1_top = df["trendline_break"].fillna(0).astype(int) == 1     # bearish break of uptrend line
        cond1_bot = df["trendline_break"].fillna(0).astype(int) == -1    # bullish break of downtrend line
    else:
        # If not using trendline condition, allow arms to form without it
        cond1_top = pd.Series(True, index=df.index)
        cond1_bot = pd.Series(True, index=df.index)

    # Persist "armed" state forward after Condition 1 occurs
    df["top_armed"] = (cond1_top & lower_high_now).replace(False, np.nan).ffill().fillna(False).infer_objects(copy=False)
    df["bottom_armed"] = (cond1_bot & higher_low_now).replace(False, np.nan).ffill().fillna(False).infer_objects(copy=False)

    # 3) Trigger when price breaches last swing low (top) or last swing high (bottom)
    break_below_last_trough = df["Close"] < last_trough
    break_above_last_peak = df["Close"] > last_peak

    top_triggered = df["top_armed"] & break_below_last_trough
    bottom_triggered = df["bottom_armed"] & break_above_last_peak

    state = np.zeros(len(df))
    state[df["top_armed"]] = 0.5
    state[df["bottom_armed"]] = -0.5
    state[top_triggered] = 1.0
    state[bottom_triggered] = -1.0

    df["123_reversal_state"] = state

    # Clean up helper columns
    df.drop(columns=["top_armed", "bottom_armed"], inplace=True)
    return df


def apply_2b_rule(df: pd.DataFrame, lookback: int = 5) -> pd.DataFrame:
    """
    Applies Sperandeo's 2B rule (failed breakout/breakdown) with a configurable lookback.
    Adds 2B amplitude (strength) based on overshoot relative to the reference swing level.

    Output:
      2b_signal:
        +1: 2B Top (failed breakout) -> Bearish
        -1: 2B Bottom (failed breakdown) -> Bullish
         0: none
      2b_strength: overshoot/undershoot magnitude (0..)
    """
    df = df.copy()
    is_peak, is_trough = _local_extrema_flags(df)

    last_peak = df["High"].where(is_peak).ffill()
    last_trough = df["Low"].where(is_trough).ffill()

    # Breakout above last peak in recent window, then close back below that peak level -> 2B Top
    breakout = df["High"] > last_peak.shift(1)
    breakout_recent = breakout.rolling(window=lookback, min_periods=1).max().astype(bool)
    failed_breakout = df["Close"] < last_peak.shift(1)
    top_2b = breakout_recent & failed_breakout

    # Breakdown below last trough in recent window, then close back above that trough -> 2B Bottom
    breakdown = df["Low"] < last_trough.shift(1)
    breakdown_recent = breakdown.rolling(window=lookback, min_periods=1).max().astype(bool)
    failed_breakdown = df["Close"] > last_trough.shift(1)
    bottom_2b = breakdown_recent & failed_breakdown

    # De-duplicate contiguous True spans (flag only first bar of a new event)
    top_trigger = top_2b & (~top_2b.shift(1).fillna(False).infer_objects(copy=False))
    bottom_trigger = bottom_2b & (~bottom_2b.shift(1).fillna(False).infer_objects(copy=False))

    sig = np.zeros(len(df), dtype=int)
    sig[top_trigger] = 1
    sig[bottom_trigger] = -1
    df["2b_signal"] = sig

    # 2B amplitude (strength): max overshoot/undershoot in the lookback window prior to trigger
    strength = np.zeros(len(df))
    for i in np.where(top_trigger)[0]:
        ref = float(last_peak.shift(1).iloc[i]) if not np.isnan(last_peak.shift(1).iloc[i]) else np.nan
        if not np.isnan(ref) and ref != 0:
            lo = max(0, i - lookback + 1)
            overshoot = (df["High"].iloc[lo:i+1].max() - ref) / abs(ref)
            strength[i] = max(0.0, overshoot)
    for i in np.where(bottom_trigger)[0]:
        ref = float(last_trough.shift(1).iloc[i]) if not np.isnan(last_trough.shift(1).iloc[i]) else np.nan
        if not np.isnan(ref) and ref != 0:
            lo = max(0, i - lookback + 1)
            undershoot = (ref - df["Low"].iloc[lo:i+1].min()) / abs(ref)
            strength[i] = max(0.0, undershoot)
    df["2b_strength"] = strength

    return df


def apply_four_day_rule(df: pd.DataFrame) -> pd.DataFrame:
    """
    Implements the spirit of Sperandeo's Four-Day Rule:
      - After 4+ consecutive up days, the first down day is a potential bearish reversal day.
      - After 4+ consecutive down days, the first up day is a potential bullish reversal day.

    Output:
      four_day_signal:
        +1: bearish reversal day after >=4 up days
        -1: bullish reversal day after >=4 down days
         0: none
    """
    df = df.copy()
    df = quantify_consecutive_days(df)
    price_change = df["Close"].diff()
    sign = np.sign(price_change).fillna(0)

    prev_streak = df["consecutive_days"].shift(1).fillna(0)
    current_sign = sign

    bearish_reversal = (prev_streak >= 4) & (current_sign < 0)
    bullish_reversal = (prev_streak <= -4) & (current_sign > 0)

    sig = np.zeros(len(df), dtype=int)
    sig[bearish_reversal] = 1
    sig[bullish_reversal] = -1
    df["four_day_signal"] = sig
    return df


def generate_sperandeo_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Master function to compute all Sperandeo-related features.
    """
    features = df.copy()
    features = identify_trend(features)
    features = detect_trendline_break(features)
    features = apply_123_rule(features, use_trendline_condition=True)
    features = apply_2b_rule(features, lookback=5)
    features = apply_four_day_rule(features)
    return features


def construct_sperandeo_data_context(spy_ohlcv: pd.DataFrame,
                                    sperandeo_features: pd.DataFrame,
                                    market_data: pd.DataFrame,
                                    fred_data: pd.DataFrame) -> str:
    """
    Construct data context for Sperandeo technical analysis to be appended to the agent prompt.
    """
    if sperandeo_features.empty:
        return "No technical analysis data available."

    latest = sperandeo_features.iloc[-1]

    # Include 2B strength in prompt
    twob_strength = latest.get("2b_strength", 0.0)

    # Prepare SPY data for analysis (last 50 days for context)
    spy_data_str = spy_ohlcv[['Open', 'High', 'Low', 'Close', 'Volume']].tail(50).round(2).to_string()

    # Market context from existing data
    market_str = market_data.tail(10).to_string()

    data_context = f"""
CURRENT MARKET DATA:
Recent SPY Price Action (Last 50 Days):
{spy_data_str}

Current Market Context:
{market_str}

PRE-COMPUTED SPERANDEO INDICATORS (Latest Day):
- Trend State: {latest['trend_state']:.1f} (1: Uptrend, -1: Downtrend, 0: Sideways)
- 1-2-3 Reversal: {latest['123_reversal_state']:.1f} (1: Top Triggered, 0.5: Top Armed, -1: Bottom Triggered, -0.5: Bottom Armed)
- 2B Pattern: {latest['2b_signal']:.1f} (1: 2B Top/Bearish, -1: 2B Bottom/Bullish)
- 2B Strength: {twob_strength:.3f} (Magnitude of the false breakout)
- Four-Day Rule: {latest.get('four_day_signal', 0):.1f} (1: Bearish, -1: Bullish)
- Trendline Break: {latest.get('trendline_break', 0):.1f} (+1: Bearish break, -1: Bullish break)
- Consecutive Days: {latest['consecutive_days']:.1f}
"""

    return data_context

# --- STRUCTURED COMMUNICATION PROTOCOL ---

@dataclass
class AnalystReport:
    """Structured report format for analyst agents"""
    analyst_type: str
    ticker: str
    date: str
    key_findings: List[str]
    metrics: Dict[str, Any]
    recommendation: str
    confidence: float
    raw_data: Optional[Dict] = None

@dataclass
class ResearcherOpinion:
    """Structured format for researcher debate contributions"""
    perspective: str  # "bullish" or "bearish"
    key_arguments: List[str]
    supporting_evidence: Dict[str, Any]
    risk_assessment: str
    confidence: float

@dataclass
class TradingDecision:
    """Structured trading decision format"""
    action: str  # "BUY_CALL", "BUY_PUT", "NO_TRADE"
    strategy: str
    dte: int
    confidence: float
    reasoning: str
    risk_assessment: Dict[str, float]

@dataclass
class RiskAssessment:
    """Risk management team assessment"""
    risk_profile: str  # "aggressive", "neutral", "conservative"
    assessment: str
    recommended_adjustments: List[str]
    risk_score: float

# --- AGENT STATE MANAGEMENT ---

class AgentState:
    """Global state management for all agents"""
    def __init__(self):
        self.analyst_reports = {}
        self.researcher_opinions = []
        self.debate_history = []
        self.trading_decision = None
        self.risk_assessments = []
        self.final_decision = None
        # Structured communication protocol store (per TradingAgents)
        self.protocol = {
            "analyst_reports": {},
            "research_debate": None,
            "trader_decision": None,
            "risk_assessments": [],
            "final_decision": None
        }

    def add_analyst_report(self, report: AnalystReport):
        self.analyst_reports[report.analyst_type] = report
        # Mirror into structured protocol as a plain dict
        if hasattr(self, "protocol"):
            self.protocol.setdefault("analyst_reports", {})
            self.protocol["analyst_reports"][report.analyst_type] = asdict(report)

    def add_researcher_opinion(self, opinion: ResearcherOpinion):
        self.researcher_opinions.append(opinion)

    def add_debate_turn(self, speaker: str, message: str):
        self.debate_history.append({"speaker": speaker, "message": message})

    def record_protocol(self, key: str, value: Any):
        """Record an item into the structured communication protocol."""
        if not hasattr(self, "protocol"):
            self.protocol = {}
        self.protocol[key] = value

    def get_analyst_summary(self) -> str:
        """Generate structured summary of all analyst reports"""
        summary = []
        for analyst_type, report in self.analyst_reports.items():
            summary.append(f"\n{analyst_type.upper()} ANALYSIS:")
            summary.append(f"Key Findings: {'; '.join(report.key_findings)}")
            summary.append(f"Recommendation: {report.recommendation}")
            summary.append(f"Confidence: {report.confidence:.2f}")
        return "\n".join(summary)

# --- AGENT SYSTEM PROMPTS (Version 2.0) ---

AGENT_PROMPTS = {
    "technical_analyst": """
You are Victor Sperandeo ("Trader Vic"), the legendary trader and author of "Trader Vic - Methods of a Wall Street Master." Your analysis is disciplined, methodical, and grounded in your specific rules for identifying trend reversals.

Your task is to provide a comprehensive technical analysis of SPY using your signature methodology for the Option Colab trading system.

CONFIDENCE CALIBRATION INSTRUCTIONS:
You are making important financial decisions, thus you should avoid giving wrong analysis with high confidence. Be very cautious and tend to give lower confidence when:
- Sperandeo rules give conflicting signals
- Volume doesn't confirm price action
- Trendlines are unclear or multiple timeframes show different trends
- Historical precedent is weak or market conditions are unusual

CONFIDENCE SCORING GUIDELINES (0-100 scale):
- High Confidence (80-100): All three Sperandeo rules align, clear trendline break with volume confirmation, strong consistent momentum
- Medium Confidence (50-79): Two of three rules confirm, trendline break with moderate volume, generally consistent technical picture
- Low Confidence (20-49): Only one rule confirms, unclear trendline status, mixed momentum signals, conflicting technical indicators
- Very Low Confidence (0-19): Highly conflicting signals, poor data quality, no clear precedent

METHODOLOGY:
At the heart of your approach is disciplined trend change confirmation using these specific technical indicators:

TREND LINE ANALYSIS: You employ objective trend line drawing:
- Uptrend: Line from lowest low to highest minor low preceding the highest high
- Downtrend: Line from highest high to lowest minor high before the lowest low
- Breaking of this trend line is the initial signal of potential trend change

THE 1-2-3 RULE (Your signature three-step reversal framework):
- Step 1: Break of the established trend line
- Step 2: Test of the high/low - price attempts to retest recent high/low but fails
- Step 3: Move beyond previous minor price point
- ALL THREE conditions must be fulfilled to confirm trend reversal

THE 2B PATTERN (Your most heavily weighted criterion):
- Price moves beyond previous high/low but fails to sustain breakout and quickly reverses
- This "false breakout" indicates momentum loss and is your strongest indicator

THE FOUR-DAY RULE (Market climax identification):
- Four consecutive days moving in trend direction, followed by reversal
- Suggests high probability of trend change within intermediate trend

ANALYSIS REQUIREMENTS WITH CONFIDENCE SCORING:
1. **Primary Trend Assessment**: What is the current dominant trend? Has the trendline been broken? (Confidence: 0-100%)
2. **1-2-3 Rule Status**: Is a 1-2-3 reversal pattern forming or confirmed? (Confidence: 0-100%)
3. **2B Pattern Analysis**: Do you see evidence of a 2B top or bottom? This is your most important criterion. (Confidence: 0-100%)
4. **Key Support/Resistance Levels**: Identify critical price levels based on your swing analysis. (Confidence: 0-100%)
5. **Recent Momentum**: Analyze the last 5-10 trading days for momentum shifts. (Confidence: 0-100%)
6. **Overall Market Bias**: Based on your complete analysis, state your market bias: Bullish, Bearish, or Neutral. (Overall Confidence: 0-100%)

UNCERTAINTY ACKNOWLEDGMENT: Explicitly acknowledge any uncertainties, data limitations, conflicting signals, or factors that reduce your confidence in the analysis.

Provide your analysis in your characteristic disciplined, methodical style. Be specific about what you see in the price action and why it matters according to your proven methodology. Express confidence levels for each key finding and acknowledge uncertainty where appropriate.
""",
    "economist_sentiment_analyst": """
You are a Macroeconomist and Sentiment Specialist. Your task is to analyze the provided economic (FRED) and volatility (VIX) data to paint a picture of the overall market mood and economic health.

CONFIDENCE CALIBRATION INSTRUCTIONS:
You are making important financial decisions, thus you should avoid giving wrong analysis with high confidence. Be very cautious and tend to give lower confidence when:
- FRED indicators give mixed or conflicting signals
- Economic data conflicts with market sentiment (VIX)
- Policy changes or external events could invalidate historical correlations
- Data quality is poor or sample sizes are insufficient

CONFIDENCE SCORING GUIDELINES (0-100 scale):
- High Confidence (80-100): All FRED indicators align, strong historical correlation with market moves, VIX and economic data consistent, clear economic trend
- Medium Confidence (50-79): Most FRED indicators align, moderate historical correlation, some divergence between VIX and economic data, generally consistent picture
- Low Confidence (20-49): FRED indicators give mixed signals, weak historical correlation, significant divergence between indicators, unclear economic trend
- Very Low Confidence (0-19): Highly conflicting economic signals, no historical precedent, overwhelming contradictory evidence

ANALYSIS REQUIREMENTS WITH CONFIDENCE SCORING:
1.  **Economic Outlook:** Based on the Treasury Yield Spread, Economic Policy Uncertainty, Employment data (Payrolls, Initial Claims), and Consumer Sentiment, what is the current state of the economy? Are conditions expanding, contracting, or showing mixed signals? (Confidence: 0-100%)
2.  **Market Sentiment:** Interpret the VIX level and its recent trend alongside the Equity Market Volatility Tracker. Is the market fearful or complacent? Are volatility measures rising or falling? (Confidence: 0-100%)
3.  **Policy and Uncertainty:** Analyze the Economic Policy Uncertainty Index - how is policy uncertainty affecting market sentiment and economic conditions? (Confidence: 0-100%)
4.  **Employment and Consumer Health:** Examine payroll employment, initial claims, and consumer sentiment - what do these indicate about economic momentum? (Confidence: 0-100%)
5.  **Synthesis:** Combine all economic and sentiment data. Is the market sentiment justified by the economic reality? Are there any divergences between different indicators? (Overall Confidence: 0-100%)

UNCERTAINTY ACKNOWLEDGMENT: Explicitly acknowledge any uncertainties, data limitations, conflicting economic signals, or factors that reduce your confidence in the analysis.

Provide your output as a single, coherent report that synthesizes all available economic and market sentiment indicators. Express confidence levels for each key finding and acknowledge uncertainty where appropriate.
""",
    "option_pricing_analyst": f"""
You are a quantitative analyst specializing in options volatility. Your task is to assess whether buying long SPY options is favorable based on the relationship between implied volatility (VIX) and historical volatility (HV). You must adhere to the fund's research findings.

**Core Logic:**
1.  **Compare VIX to HV:** The primary indicator is the spread between VIX and the {HV_WINDOW}-day HV.
2.  **Assess Favorability:**
    - If VIX is more than 10% above HV (e.g., VIX=20, HV=18), options are considered 'overpriced'. The premium drag makes buying them **unfavorable**.
    - If VIX is below HV, options are 'underpriced' and buying is **favorable**.
    - The default bias is **unfavorable** due to the consistent underperformance of long options found in academic research. You need a compelling reason (like VIX < HV) to call it favorable.
3.  **Directional Consideration:** A very high VIX might be acceptable for a bearish long put strategy if a crash is anticipated, but it is highly unfavorable for a long call strategy. Mention this nuance.
4.  **Carry Cost:** Note the impact of the risk-free rate (^IRX). A higher rate increases the carry cost for long option positions, making it an additional headwind for buyers.
5.  **Expiry Suggestion:**
    - If VIX is high or has spiked recently, suggest **'near-term'** expiries (<= 43 DTE) to capture the immediate move.
    - If VIX is low or compressed, suggest **'long-term'** expiries (> 43 DTE) for a better value proposition.

Your output MUST be a single JSON object with the following structure:
{{
    "vol_assessment": "'favorable' or 'unfavorable'",
    "calibrated_confidence": "An integer from 0 (no confidence in assessment) to 100 (absolute confidence)",
    "reasoning": "A detailed explanation covering your VIX vs. HV comparison, the impact of the risk-free rate, and the default unfavorable bias.",
    "suggested_expiry_type": "'near-term' or 'long-term'"
}}
""",
    "bullish_strategist": """
You are an aggressive and optimistic Bullish Strategist. Using the provided reports from the Technical, Economic, and Options analysts, build the strongest possible case for a positive (upward) move in the market.

-   Focus on any data point that supports a bullish outlook (e.g., price at support, positive economic data, favorable volatility).
-   Downplay or re-interpret any negative data.
-   Conclude with a clear "Bullish Case" summary and a confidence score for your argument.

Your output must be a text report ending with the line:
CONFIDENCE: [a number from 1 to 10]
""",
    "bearish_strategist": """
You are a cautious and pessimistic Bearish Strategist. Using the provided reports from the Technical, Economic, and Options analysts, build the strongest possible case for a negative (downward) move in the market.

-   Focus on any data point that supports a bearish outlook (e.g., price at resistance, negative economic data, unfavorable volatility).
-   Downplay or re-interpret any positive data.
-   Conclude with a clear "Bearish Case" summary and a confidence score for your argument.

Your output must be a text report ending with the line:
CONFIDENCE: [a number from 1 to 10]
""",
    "chief_trader": f"""
You are the Chief Trader. You have received reports from your Technical, Economic, and Option Pricing analysts, the conflicting arguments from your strategists, and information on your current portfolio.

CONFIDENCE AGGREGATION AND RISK MANAGEMENT:
You are making important financial decisions with real capital at risk, thus you should avoid making high-risk trades with high confidence when analyst confidence levels are low or conflicting. Be very cautious when:
- Multiple analysts have low confidence scores (<50%)
- Analysts give conflicting recommendations
- Market conditions are uncertain or unprecedented
- Option pricing is unfavorable according to volatility analysis

CONFIDENCE AGGREGATION METHODOLOGY:
1. **Analyst Confidence Assessment**: Review each analyst's confidence level and the quality of their supporting evidence
2. **Consensus Analysis**: Determine if analysts agree or conflict in their recommendations
3. **Risk-Adjusted Confidence**: Lower your overall confidence when facing uncertainty, conflicting signals, or unfavorable risk/reward
4. **Cautious Steering**: When aggregate analyst confidence is low (<60%), strongly consider "No Trade" or reduced position sizing

Your task is to synthesize all this information and make a single, decisive, and actionable trading decision for SPY options for the next trading day. You must weigh all evidence, including the quantitative assessment of option pricing and analyst confidence levels.

Your final output MUST be a single JSON object with the following structure:
{{
    "decision": "BULLISH", "BEARISH", or "NEUTRAL",
    "strategy": "A specific options strategy (e.g., 'Buy Call', 'Buy Put', 'Bull Call Spread', 'Bear Put Spread', 'Iron Condor', 'No Trade')",
    "dte": An integer representing the chosen Days to Expiration from this list: {DTE_CHOICES},
    "confidence": An integer from 1 (very low) to 10 (very high) in your overall decision,
    "analyst_confidence_summary": "Brief summary of each analyst's confidence level and how it influenced your decision",
    "uncertainty_factors": "List any significant uncertainties or conflicting signals that reduce confidence",
    "risk_assessment": "Assessment of potential downside if the trade goes wrong",
    "reasoning": "A detailed paragraph explaining how you synthesized ALL inputs (technical, economic, volatility, strategist debate, analyst confidence levels, and current position) to arrive at your final decision."
}}
"""
}

# --- SPECIALIZED AGENT IMPLEMENTATIONS ---

class BaseAgent(LLMAgent):
    """Base class for all agents with structured output"""

    def __init__(self, model: str, system_prompt: str, agent_type: str):
        super().__init__(model, system_prompt)
        self.agent_type = agent_type

    def generate_structured_response(self, user_prompt: str,
                                    response_class: type) -> Any:
        """Generate response and parse into structured format"""
        response = self.generate_response(user_prompt, is_json=True)
        if response:
            try:
                return response_class(**response)
            except Exception as e:
                logging.error(f"Failed to parse response: {e}")
                return None
        return None

class TechnicalAnalystAgent(BaseAgent):
    """Enhanced technical analyst with Sperandeo methodology"""

    def __init__(self):
        prompt = AGENT_PROMPTS["technical_analyst"]
        super().__init__(DEEP_THINKING_MODEL, prompt, "technical")

    def analyze(self, data: Dict, state: AgentState) -> AnalystReport:
        """Perform technical analysis and return structured report"""
        sperandeo_features = data.get('sperandeo_features')

        if sperandeo_features is not None:
            context = construct_sperandeo_data_context(
                data['spy_ohlcv'],
                sperandeo_features,
                data['market_data'],
                data['fred_data']
            )
        else:
            context = self._format_basic_context(data)

        prompt = f"""Analyze the following market data and provide a structured JSON report following the confidence calibration guidelines in your system prompt:

{context}

Return a JSON object with:
- key_findings: list of 3-5 key technical observations with individual confidence levels
- metrics: dict with important technical indicators
- recommendation: "BULLISH", "BEARISH", or "NEUTRAL"
- confidence: float between 0 and 1 (your overall confidence in the analysis)
- uncertainty_factors: list of any uncertainties or conflicting signals
- confidence_breakdown: dict with confidence levels for each Sperandeo rule analysis
"""

        response = self.generate_response(prompt, is_json=True)

        return AnalystReport(
            analyst_type="technical",
            ticker="SPY",
            date=datetime.now().strftime('%Y-%m-%d'),
            key_findings=response.get('key_findings', []),
            metrics=response.get('metrics', {}),
            recommendation=response.get('recommendation', 'NEUTRAL'),
            confidence=response.get('confidence', 0.5)
        )

class EconomistSentimentAnalystAgent(BaseAgent):
    """Economist analyzing FRED data and market sentiment"""

    def __init__(self):
        prompt = AGENT_PROMPTS["economist_sentiment_analyst"]
        super().__init__(DEEP_THINKING_MODEL, prompt, "economist")

    def analyze(self, data: Dict, state: AgentState) -> AnalystReport:
        """Analyze economic indicators and sentiment"""
        fred_signals = data.get('fred_signals', {})

        context = f"""
FRED Economic Indicators (Normalized -1 to +1):
Date: {fred_signals.get('date', 'N/A')}

Values:
{json.dumps(fred_signals.get('values', {}), indent=2)}

Trends (30-day changes):
{json.dumps(fred_signals.get('trends', {}), indent=2)}

Signal Strength:
{json.dumps(fred_signals.get('strength', {}), indent=2)}

VIX Level: {data['market_data']['VIX'].iloc[-1]:.2f}
Risk-Free Rate: {data['market_data'].get('^IRX', pd.Series([3.5])).iloc[-1]:.2f}%

Provide structured analysis focusing on economic outlook and market sentiment.
"""

        prompt = f"""{context}

Return a JSON object following the confidence calibration guidelines in your system prompt:
- key_findings: list of 3-5 key economic observations with individual confidence levels
- metrics: dict with important economic indicators
- recommendation: "RISK_ON", "RISK_OFF", or "NEUTRAL"
- confidence: float between 0 and 1 (your overall confidence in the analysis)
- uncertainty_factors: list of any uncertainties or conflicting economic signals
- confidence_breakdown: dict with confidence levels for each economic indicator analysis
"""

        response = self.generate_response(prompt, is_json=True)

        return AnalystReport(
            analyst_type="economist",
            ticker="SPY",
            date=datetime.now().strftime('%Y-%m-%d'),
            key_findings=response.get('key_findings', []),
            metrics=response.get('metrics', {}),
            recommendation=response.get('recommendation', 'NEUTRAL'),
            confidence=response.get('confidence', 0.5)
)


class AlphaVantageNewsClient:
    """Lightweight client for Alpha Vantage NEWS_SENTIMENT."""
    BASE_URL = "https://www.alphavantage.co/query"

    def __init__(self, api_key: str):
        self.api_key = api_key

    def fetch_news(self, topics: str, time_from: str = None, time_to: str = None,
                   sort: str = "LATEST", limit: int = 100) -> dict:
        params = {
            "function": "NEWS_SENTIMENT",
            "apikey": self.api_key,
            "sort": sort,
            "limit": limit
        }
        if topics:
            params["topics"] = topics
        if time_from:
            params["time_from"] = time_from
        if time_to:
            params["time_to"] = time_to
        try:
            resp = requests.get(self.BASE_URL, params=params, timeout=20)
            resp.raise_for_status()
            return resp.json()
        except Exception as e:
            logging.error(f"Alpha Vantage NEWS_SENTIMENT error: {e}")
            return {"feed": []}


class AlphaVantageBaseAnalyst(BaseAgent):
    """Base for Alpha Vantagepowered analysts with shared helpers."""
    def __init__(self, api_key: str, role: str, topics: str):
        super().__init__(DEEP_THINKING_MODEL, f"You are a {role} using Alpha Vantage news sentiment.", role)
        self.client = AlphaVantageNewsClient(api_key)
        self.topics = topics

    def _aggregate(self, feed: List[dict]) -> dict:
        scores, labels = [], []
        bull = bear = neutral = 0
        for item in feed or []:
            score = item.get("overall_sentiment_score")
            label = item.get("overall_sentiment_label")
            if isinstance(score, str):
                try:
                    score = float(score)
                except:  # noqa
                    score = None
            if score is not None:
                scores.append(score)
            if label:
                labels.append(label)
                if label.upper() == "POSITIVE":
                    bull += 1
                elif label.upper() == "NEGATIVE":
                    bear += 1
                else:
                    neutral += 1
        avg = float(np.mean(scores)) if scores else 0.0

        # Enhanced confidence calibration based on data quality and volume
        n_articles = len(feed or [])
        confidence_raw = abs(avg) if avg != 0 else 0.0

        # Adjust confidence based on sample size (research-based calibration)
        if n_articles >= 500:
            sample_confidence = 0.9  # High confidence with large sample
        elif n_articles >= 100:
            sample_confidence = 0.7  # Medium confidence
        elif n_articles >= 50:
            sample_confidence = 0.5  # Lower confidence
        else:
            sample_confidence = 0.3  # Very low confidence with small sample

        # Adjust confidence based on sentiment consistency
        total_labeled = bull + bear + neutral
        if total_labeled > 0:
            dominant_sentiment = max(bull, bear, neutral) / total_labeled
            consistency_confidence = dominant_sentiment  # Higher when sentiment is consistent
        else:
            consistency_confidence = 0.3

        # Final calibrated confidence (0-100 scale)
        calibrated_confidence = min(100, max(0, confidence_raw * sample_confidence * consistency_confidence * 100))

        return {
            "avg_overall_score": avg,
            "bull": bull,
            "bear": bear,
            "neutral": neutral,
            "n": n_articles,
            "calibrated_confidence": calibrated_confidence,
            "sample_confidence": sample_confidence,
            "consistency_confidence": consistency_confidence
        }


class AlphaVantageSentimentAnalyst(AlphaVantageBaseAnalyst):
    def __init__(self, api_key: str):
        super().__init__(api_key, role="sentiment analyst", topics="financial_markets,economy_macro")

    def analyze(self, data: Dict, state: AgentState) -> AnalystReport:
        asof = data.get("asof_date", datetime.utcnow())
        time_to = asof.strftime('%Y%m%dT%H%M')
        time_from = (asof - timedelta(days=190)).strftime('%Y%m%dT%H%M')
        payload = self.client.fetch_news(self.topics, time_from=time_from, time_to=time_to, sort="LATEST", limit=1000)
        feed = payload.get("feed", [])
        metrics = self._aggregate(feed)

        # Enhanced key findings with confidence awareness
        key_findings = [
            f"Avg sentiment score: {metrics['avg_overall_score']:.3f} (Confidence: {metrics['calibrated_confidence']:.0f}%)",
            f"Bull/Bear/Neutral counts: {metrics['bull']}/{metrics['bear']}/{metrics['neutral']} (n={metrics['n']})",
            f"Data quality: Sample size confidence: {metrics['sample_confidence']:.1f}, Consistency: {metrics['consistency_confidence']:.1f}"
        ]

        # Enhanced rule-based recommendation with confidence consideration
        rec = "NEUTRAL"
        confidence_threshold = 50  # Only make directional calls with reasonable confidence

        if metrics["calibrated_confidence"] >= confidence_threshold:
            if metrics["avg_overall_score"] >= 0.1 and metrics["bull"] > metrics["bear"]:
                rec = "BULLISH"
            elif metrics["avg_overall_score"] <= -0.1 and metrics["bear"] > metrics["bull"]:
                rec = "BEARISH"

        # Add uncertainty acknowledgment for low confidence scenarios
        if metrics["calibrated_confidence"] < confidence_threshold:
            key_findings.append(f"UNCERTAINTY: Low confidence ({metrics['calibrated_confidence']:.0f}%) due to limited data or mixed signals")

        return AnalystReport(
            analyst_type="sentiment",
            ticker="SPY",
            date=datetime.now().strftime('%Y-%m-%d'),
            key_findings=key_findings,
            metrics=metrics,
            recommendation=rec,
            confidence=metrics["calibrated_confidence"] / 100.0,  # Convert to 0-1 scale for AnalystReport
            raw_data={"sample": feed[:5]}  # keep small sample for context
        )


class AlphaVantageNewsAnalyst(AlphaVantageBaseAnalyst):
    def __init__(self, api_key: str):
        super().__init__(api_key, role="news analyst", topics="economy_monetary,economy_fiscal")

    def analyze(self, data: Dict, state: AgentState) -> AnalystReport:
        asof = data.get("asof_date", datetime.utcnow())
        time_to = asof.strftime('%Y%m%dT%H%M')
        time_from = (asof - timedelta(days=190)).strftime('%Y%m%dT%H%M')
        payload = self.client.fetch_news(self.topics, time_from=time_from, time_to=time_to, sort="RELEVANCE", limit=1000)
        feed = payload.get("feed", [])
        metrics = self._aggregate(feed)

        # Enhanced key findings with confidence awareness for news impact
        key_findings = [
            f"Avg macro news sentiment: {metrics['avg_overall_score']:.3f} (Confidence: {metrics['calibrated_confidence']:.0f}%)",
            f"Distribution B/B/N: {metrics['bull']}/{metrics['bear']}/{metrics['neutral']} (n={metrics['n']})",
            f"News impact assessment: Sample confidence: {metrics['sample_confidence']:.1f}, Consistency: {metrics['consistency_confidence']:.1f}"
        ]

        # Enhanced rule-based recommendation with confidence consideration for news impact
        rec = "NEUTRAL"
        confidence_threshold = 60  # Higher threshold for news impact due to complexity

        if metrics["calibrated_confidence"] >= confidence_threshold:
            if metrics["avg_overall_score"] >= 0.15 and metrics["bull"] >= (metrics["bear"] + 2):
                rec = "BULLISH"
            elif metrics["avg_overall_score"] <= -0.15 and metrics["bear"] >= (metrics["bull"] + 2):
                rec = "BEARISH"

        # Add uncertainty acknowledgment for news impact assessment
        if metrics["calibrated_confidence"] < confidence_threshold:
            key_findings.append(f"UNCERTAINTY: Low confidence ({metrics['calibrated_confidence']:.0f}%) in news impact due to ambiguous signals or limited relevant news")

        # Additional news-specific uncertainty factors
        if metrics["n"] < 50:
            key_findings.append("CAUTION: Limited news volume may not represent full market impact")

        return AnalystReport(
            analyst_type="news",
            ticker="SPY",
            date=datetime.now().strftime('%Y-%m-%d'),
            key_findings=key_findings,
            metrics=metrics,
            recommendation=rec,
            confidence=metrics["calibrated_confidence"] / 100.0,  # Convert to 0-1 scale for AnalystReport
            raw_data={"sample": feed[:5]}
        )

class DebateFacilitator:
    """Manages structured debates between agents"""

    def __init__(self, model: str = DEBATE_MODEL):
        self.model = model

    def facilitate_debate(self, bull_agent: BaseAgent, bear_agent: BaseAgent,
                         context: str, rounds: int = DEBATE_ROUNDS) -> Dict:
        """Facilitate multi-round debate and determine winner"""
        debate_history = []

        for round_num in range(rounds):
            # Bull argument
            bull_prompt = f"""Round {round_num + 1}: Make your bullish case.

Context: {context}

Previous debate: {json.dumps(debate_history, indent=2) if debate_history else 'First round'}

Provide compelling bullish arguments in 200 words or less."""

            bull_response = bull_agent.generate_response(bull_prompt)
            debate_history.append({"round": round_num + 1, "bull": bull_response})

            # Bear argument
            bear_prompt = f"""Round {round_num + 1}: Make your bearish case and respond to bull arguments.

Context: {context}

Bull's argument: {bull_response}

Provide compelling bearish arguments in 200 words or less."""

            bear_response = bear_agent.generate_response(bear_prompt)
            debate_history.append({"round": round_num + 1, "bear": bear_response})

        # Determine prevailing perspective
        summary_prompt = f"""Analyze this debate and determine the prevailing perspective:

{json.dumps(debate_history, indent=2)}

Return JSON with:
- prevailing_view: "BULLISH" or "BEARISH"
- confidence: float 0-1
- key_points: list of winning arguments
"""

        facilitator = LLMAgent(self.model, "You are an impartial debate judge.")
        result = facilitator.generate_response(summary_prompt, is_json=True)

        return {
            "debate_history": debate_history,
            "result": result
        }

class RiskManagementTeam:
    """Three-perspective risk management team"""

    def __init__(self):
        self.aggressive = BaseAgent(
            DEEP_THINKING_MODEL,
            "You are an aggressive risk manager focused on maximizing returns.",
            "aggressive"
        )
        self.neutral = BaseAgent(
            DEEP_THINKING_MODEL,
            "You are a balanced risk manager seeking optimal risk-reward.",
            "neutral"
        )
        self.conservative = BaseAgent(
            DEEP_THINKING_MODEL,
            "You are a conservative risk manager focused on capital preservation.",
            "conservative"
        )

    def assess_decision(self, decision: TradingDecision,
                       state: AgentState) -> List[RiskAssessment]:
        """Get risk assessments from all three perspectives"""
        assessments = []

        context = f"""
Trading Decision: {json.dumps(asdict(decision), indent=2)}

Analyst Reports Summary:
{state.get_analyst_summary()}

Assess this decision from your risk perspective.
"""

        for agent, profile in [(self.aggressive, "aggressive"),
                               (self.neutral, "neutral"),
                               (self.conservative, "conservative")]:

            prompt = f"""{context}

Provide risk assessment as JSON:
- assessment: your detailed assessment
- recommended_adjustments: list of suggested adjustments
- risk_score: float 0-10 (10 = highest risk)
"""

            response = agent.generate_response(prompt, is_json=True)

            assessments.append(RiskAssessment(
                risk_profile=profile,
                assessment=response.get('assessment', ''),
                recommended_adjustments=response.get('recommended_adjustments', []),
                risk_score=response.get('risk_score', 5.0)
            ))

        return assessments

class ChiefTrader(BaseAgent):
    """Enhanced chief trader with structured decision-making"""

    def __init__(self):
        prompt = AGENT_PROMPTS["chief_trader"]
        super().__init__(DEEP_THINKING_MODEL, prompt, "trader")

    def make_decision(self, state: AgentState) -> TradingDecision:
        """Synthesize all information and make trading decision"""

        # Prepare context from all sources
        context = f"""
ANALYST REPORTS:
{state.get_analyst_summary()}

RESEARCHER DEBATE OUTCOME:
{json.dumps(state.debate_history[-1] if state.debate_history else {}, indent=2)}

MARKET CONDITIONS:
- VIX Level: High/Medium/Low
- Option Pricing: Favorable/Unfavorable
- DTE Choices: {DTE_CHOICES}

Make a specific options trading decision for SPY.
"""

        prompt = f"""{context}

Return JSON decision:
- action: "BUY_CALL", "BUY_PUT", "SPREAD", or "NO_TRADE"
- strategy: specific strategy name
- dte: chosen DTE from available choices
- confidence: 0-10
- reasoning: detailed explanation
"""

        response = self.generate_response(prompt, is_json=True)

        return TradingDecision(
            action=response.get('action', 'NO_TRADE'),
            strategy=response.get('strategy', 'None'),
            dte=response.get('dte', 30),
            confidence=response.get('confidence', 5),
            reasoning=response.get('reasoning', ''),
            risk_assessment={}
        )

class FundManager(BaseAgent):
    """Final approval authority"""

    def __init__(self):
        prompt = """You are the Fund Manager with final approval authority.
        Review all analyses, debates, and risk assessments to make the final decision."""
        super().__init__(DEEP_THINKING_MODEL, prompt, "manager")

    def approve_decision(self, decision: TradingDecision,
                        risk_assessments: List[RiskAssessment],
                        state: AgentState) -> Dict:
        """Final approval or modification of trading decision"""

        context = f"""
PROPOSED DECISION:
{json.dumps(asdict(decision), indent=2)}

RISK ASSESSMENTS:
{json.dumps([asdict(ra) for ra in risk_assessments], indent=2)}

ALL REPORTS:
{state.get_analyst_summary()}

Provide final approval or modifications.
"""

        prompt = f"""{context}

Return final decision as JSON:
- approved: true/false
- final_action: approved action
- final_strategy: approved strategy
- final_dte: approved DTE
- modifications: list of any modifications made
- rationale: explanation of decision
"""

        return self.generate_response(prompt, is_json=True)


    # Optional MCP override: if provided, delegate data retrieval
    # to MCP tool to reduce prompt size and centralize context.
    # Must return a dict matching get_market_data() schema when used.


# --- CORE FRAMEWORK CLASSES ---


class DataFetcher:
    """Handles all external data fetching and pre-processing, including HV calculation."""
    def __init__(self, mcp_context=None):
        self.data_cache = {}
        self.mcp = mcp_context

    def _calculate_hv(self, series: pd.Series) -> pd.Series:
        """Calculates annualized historical volatility."""
        log_returns = np.log(series / series.shift(1))
        hv = log_returns.rolling(window=HV_WINDOW).std() * np.sqrt(252)
        return hv * 100 # Return as percentage

    def get_market_data(self, current_date: datetime) -> dict:
        date_str = current_date.strftime('%Y-%m-%d')
        if date_str in self.data_cache:
            return self.data_cache[date_str]

        # If MCP is provided and implements get_market_data, prefer it
        if hasattr(self, 'mcp') and self.mcp and hasattr(self.mcp, 'get_market_data'):
            try:
                mcp_result = self.mcp.get_market_data(current_date)
                if mcp_result:
                    self.data_cache[date_str] = mcp_result
                    return mcp_result
            except Exception as e:
                logging.warning(f"MCP get_market_data failed, falling back: {e}")

        logging.info(f"Fetching data for window ending on {date_str}...")
        end_date = current_date
        start_date = end_date - timedelta(days=ANALYSIS_WINDOW_DAYS * 1.7)

        try:
            market_data_raw = yf.download(MARKET_TICKERS, start=start_date, end=end_date, progress=False)
            if market_data_raw.empty:
                logging.error(f"No market data found for the period ending {date_str}.")
                return None

            market_data = market_data_raw['Close'].tail(ANALYSIS_WINDOW_DAYS).copy()
            market_data['HV'] = self._calculate_hv(market_data['SPY'])

            # Fetch OHLCV data for SPY for Sperandeo analysis (extended window for technical analysis)
            try:
                spy_ohlcv = get_spy_market_data("SPY", days=126)
                logging.info(f"Fetched SPY OHLCV data: {len(spy_ohlcv)} rows")
            except Exception as e:
                logging.warning(f"Failed to fetch SPY OHLCV data: {e}. Using Close data only.")
                spy_ohlcv = None

            fred_data = web.DataReader(list(FRED_SERIES.keys()), 'fred', start_date, end_date)
            fred_data = fred_data.tail(ANALYSIS_WINDOW_DAYS)

            # Fill NaNs robustly (using newer pandas methods)
            for df in [market_data, fred_data]:
                df.ffill(inplace=True)
                df.bfill(inplace=True)

            if market_data.isnull().values.any() or fred_data.isnull().values.any():
                 logging.critical(f"Unrecoverable NaN values in data for {date_str}. Skipping.")
                 return None

            result = {
                "market_data": market_data,
                "fred_data": fred_data,
                "spy_ohlcv": spy_ohlcv  # Add OHLCV data for Sperandeo analysis
            }
            self.data_cache[date_str] = result
            return result

        except Exception as e:
            logging.error(f"Error fetching data for {date_str}: {e}")
            return None

class EnhancedDataFetcher(DataFetcher):
    """Augmented fetcher that assembles the full TradingAgents data bundle.
    Returns a dict including market_data, fred_data, spy_ohlcv, fred_processed,
    fred_signals, and sperandeo_features.
    """
    def get_comprehensive_data(self, current_date: datetime) -> Optional[dict]:
        base = self.get_market_data(current_date)
        if not base:
            return None
        # Normalize FRED indicators and derive signals (uses FRED API)
        try:
            fred_processed, fred_normalized_only = normalize_fred_indicators(
                api_key=FRED_API_KEY,
                lookback_periods=6,
                end_date=current_date.strftime('%Y-%m-%d')
            )
            fred_signals = get_latest_signals(fred_processed, lookback_days=180)
        except Exception as e:
            logging.warning(f"FRED normalization failed: {e}")
            fred_processed, fred_signals = pd.DataFrame(), {}
        # Generate Sperandeo features if OHLCV available
        features = pd.DataFrame()
        if base.get('spy_ohlcv') is not None and not base['spy_ohlcv'].empty:
            try:
                features = generate_sperandeo_features(base['spy_ohlcv'])
            except Exception as e:
                logging.warning(f"Sperandeo feature generation failed: {e}")
        # Assemble comprehensive payload
        bundle = {
            "market_data": base["market_data"],
            "fred_data": base["fred_data"],
            "spy_ohlcv": base.get("spy_ohlcv"),
            "fred_processed": fred_processed,
            "fred_signals": fred_signals,
            "sperandeo_features": features,
            "asof_date": current_date
        }
        return bundle


class LLMAgent:
    """A reusable class to interact with the OpenRouter API."""
    def __init__(self, model: str, system_prompt: str):
        if not OPENROUTER_API_KEY:
            raise ValueError("OPENROUTER_API_KEY environment variable not set.")
        self.model = model
        self.system_prompt = system_prompt

    def generate_response(self, user_prompt: str, is_json: bool = False, retries: int = 3) -> any:
        logging.info(f"Invoking LLM agent with model {self.model}...")
        for attempt in range(retries):
            try:
                response = requests.post(
                    url="https://openrouter.ai/api/v1/chat/completions",
                    headers={
                        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
                        "Content-Type": "application/json"
                    },
                    data=json.dumps({
                        "model": self.model,
                        "messages": [
                            {"role": "system", "content": self.system_prompt},
                            {"role": "user", "content": user_prompt},
                        ],
                        "temperature": 0.7,
                        "max_tokens": 4000,
                    }),
                    timeout=60
                )
                response.raise_for_status()
                result = response.json()
                content = result["choices"][0]["message"]["content"]

                if is_json:
                    return json.loads(content)
                else:
                    return content
            except json.JSONDecodeError as e:
                logging.error(f"LLM did not return valid JSON on attempt {attempt+1}. Content: {content}")
                time.sleep(2 ** attempt)
            except Exception as e:
                logging.error(f"Error calling OpenRouter API on attempt {attempt+1}: {e}")
                time.sleep(2 ** attempt)

        logging.critical(f"LLM Agent failed after {retries} attempts.")
        return None


class SperandeoTechnicalAnalyst(LLMAgent):
    """
    Enhanced Technical Analyst using Victor Sperandeo's methodology.
    Extends LLMAgent to include Sperandeo-specific data processing.
    """
    def __init__(self, model: str):
        # Initialize with the technical_analyst prompt from AGENT_PROMPTS
        super().__init__(model, AGENT_PROMPTS["technical_analyst"])

    def analyze(self, raw_data: dict) -> str:
        """
        Perform Sperandeo technical analysis on the provided market data.
        """
        try:
            # Check if we have OHLCV data for SPY
            spy_ohlcv = raw_data.get('spy_ohlcv')
            if spy_ohlcv is None or spy_ohlcv.empty:
                logging.warning("No SPY OHLCV data available. Falling back to basic analysis.")
                return self._fallback_analysis(raw_data)

            # Generate Sperandeo features
            logging.info("Generating Sperandeo technical features...")
            sperandeo_features = generate_sperandeo_features(spy_ohlcv)

            # Construct data context to append to the system prompt
            data_context = construct_sperandeo_data_context(
                spy_ohlcv,
                sperandeo_features,
                raw_data['market_data'],
                raw_data['fred_data']
            )

            # Use the standard LLMAgent generate_response method with data context
            logging.info("Requesting Sperandeo technical analysis from LLM...")
            analysis = self.generate_response(data_context)

            if analysis:
                logging.info("Sperandeo technical analysis completed successfully.")
                return analysis
            else:
                logging.error("LLM analysis failed. Using fallback.")
                return self._fallback_analysis(raw_data)

        except Exception as e:
            logging.error(f"Error in Sperandeo technical analysis: {e}")
            return self._fallback_analysis(raw_data)

    def _fallback_analysis(self, raw_data: dict) -> str:
        """
        Fallback to basic technical analysis if Sperandeo analysis fails.
        """
        market_data = raw_data.get('market_data', pd.DataFrame())
        if market_data.empty:
            return "Technical analysis unavailable due to data issues."

        try:
            spy_close = market_data.get('SPY', pd.Series())
            if spy_close.empty:
                return "SPY data unavailable for technical analysis."

            current_price = spy_close.iloc[-1]
            recent_high = spy_close.tail(20).max()
            recent_low = spy_close.tail(20).min()

            # Simple trend analysis
            sma_20 = spy_close.tail(20).mean()
            trend = "Uptrend" if current_price > sma_20 else "Downtrend"

            return f"""**TECHNICAL ANALYSIS (Fallback Mode)**

**Primary Trend:** {trend} - Current price ${current_price:.2f} vs 20-day average ${sma_20:.2f}

**Key Levels:**
- Recent High: ${recent_high:.2f}
- Recent Low: ${recent_low:.2f}
- Support/Resistance: Price is {'above' if current_price > sma_20 else 'below'} key moving average

**Recent Momentum:** {'Bullish' if current_price > sma_20 else 'Bearish'} based on price relative to recent average

Note: This is a simplified analysis due to technical issues with advanced Sperandeo methodology."""

        except Exception as e:
            logging.error(f"Error in fallback analysis: {e}")
            return "Technical analysis temporarily unavailable due to system issues."

class EnhancedBacktestEngine:
    """Orchestrates the complete TradingAgents workflow"""

    def __init__(self, start_date: str, end_date: str, mcp_context: Any = None):
        self.start_date = datetime.strptime(start_date, '%Y-%m-%d')
        self.end_date = datetime.strptime(end_date, '%Y-%m-%d')
        self.data_fetcher = EnhancedDataFetcher(mcp_context=mcp_context)
        self.results = []

        # Initialize all agents
        self.technical_analyst = TechnicalAnalystAgent()
        self.economist = EconomistSentimentAnalystAgent()

        # Initialize Alpha Vantage–powered analysts
        self.sentiment_analyst = AlphaVantageSentimentAnalyst(ALPHAVANTAGE_API_KEY)
        self.news_analyst = AlphaVantageNewsAnalyst(ALPHAVANTAGE_API_KEY)

        # Researcher team
        self.bull_researcher = BaseAgent(
            DEEP_THINKING_MODEL,
            "You are a bullish researcher advocating for positive opportunities.",
            "bull"
        )
        self.bear_researcher = BaseAgent(
            DEEP_THINKING_MODEL,
            "You are a bearish researcher highlighting risks and concerns.",
            "bear"
        )
        self.debate_facilitator = DebateFacilitator()

        # Trading team
        self.chief_trader = ChiefTrader()
        self.risk_team = RiskManagementTeam()
        self.fund_manager = FundManager()

        logging.info("Enhanced BacktestEngine initialized with full agent hierarchy")

    def run_simulation_step(self, current_date: datetime):
        """Execute complete TradingAgents workflow for one day"""
        logging.info(f"--- Running TradingAgents simulation for {current_date.strftime('%Y-%m-%d')} ---")

        # Initialize state
        state = AgentState()

        # 1. Data Collection
        data = self.data_fetcher.get_comprehensive_data(current_date)
        if not data:
            logging.error("Failed to fetch data, skipping day")
            return

        # 2. Analyst Phase (Concurrent Analysis)
        logging.info("Phase 1: Analyst Team gathering information...")

        # Technical Analysis
        tech_report = self.technical_analyst.analyze(data, state)
        state.add_analyst_report(tech_report)

        # Economic Analysis
        econ_report = self.economist.analyze(data, state)
        state.add_analyst_report(econ_report)

        # Sentiment and News Analysis (Alpha Vantage)
        try:
            sent_report = self.sentiment_analyst.analyze(data, state)
            state.add_analyst_report(sent_report)
        except Exception as e:
            logging.warning(f"Sentiment analysis failed: {e}")
        try:
            news_report = self.news_analyst.analyze(data, state)
            state.add_analyst_report(news_report)
        except Exception as e:
            logging.warning(f"News analysis failed: {e}")

        logging.info("Analyst reports completed")
        # Persist analyst reports into structured protocol
        state.record_protocol("analyst_reports", {k: asdict(v) for k, v in state.analyst_reports.items()})


        # 3. Researcher Phase (Debate)
        logging.info("Phase 2: Researcher Team debate...")

        debate_context = state.get_analyst_summary()
        debate_result = self.debate_facilitator.facilitate_debate(
            self.bull_researcher,
            self.bear_researcher,
            debate_context,
            rounds=DEBATE_ROUNDS
        )

        state.debate_history = debate_result['debate_history']
        logging.info(f"Debate concluded: {debate_result['result'].get('prevailing_view')}")
        # Persist debate outcome
        state.record_protocol("research_debate", debate_result.get('result', {}))


        # 4. Trading Decision Phase
        logging.info("Phase 3: Chief Trader making decision...")

        trading_decision = self.chief_trader.make_decision(state)
        # Persist trader decision
        state.record_protocol("trader_decision", asdict(trading_decision))

        state.trading_decision = trading_decision

        logging.info(f"Trading decision: {trading_decision.action} - {trading_decision.strategy}")

        # 5. Risk Management Phase
        logging.info("Phase 4: Risk Management Team assessment...")

        risk_assessments = self.risk_team.assess_decision(trading_decision, state)
        state.risk_assessments = risk_assessments
        # Persist risk assessments
        state.record_protocol("risk_assessments", [asdict(ra) for ra in risk_assessments])


        avg_risk = np.mean([ra.risk_score for ra in risk_assessments])
        logging.info(f"Average risk score: {avg_risk:.2f}")

        # 6. Fund Manager Approval
        logging.info("Phase 5: Fund Manager final approval...")

        final_decision = self.fund_manager.approve_decision(
            trading_decision,
            risk_assessments,
            state
        )
        state.final_decision = final_decision
        # Persist final decision
        state.record_protocol("final_decision", final_decision)


        logging.info(f"Final decision: {'APPROVED' if final_decision.get('approved') else 'MODIFIED'}")

        # 7. Log Results
        self.results.append({
            "date": current_date.strftime('%Y-%m-%d'),
            "analyst_reports": {k: asdict(v) for k, v in state.analyst_reports.items()},
            "debate_outcome": debate_result['result'],
            "trading_decision": asdict(trading_decision),
            "risk_assessments": [asdict(ra) for ra in risk_assessments],
            "final_decision": final_decision
        })

    def run_backtest(self):
        """Execute complete backtest over date range"""
        all_dates = pd.bdate_range(self.start_date, self.end_date)

        for current_date in all_dates:
            try:
                self.run_simulation_step(current_date)
                time.sleep(0.5)  # Rate limiting
            except Exception as e:
                logging.error(f"Error on {current_date}: {e}")
                continue

        logging.info("--- TradingAgents backtest complete ---")
        self.save_results()

    def save_results(self):
        """Save structured results"""
        if not self.results:
            logging.warning("No results to save")
            return

        # Save as JSON for structured data
        filename = f"tradingagents_v3_results_{self.start_date.strftime('%Y%m%d')}_{self.end_date.strftime('%Y%m%d')}.json"
        with open(filename, 'w') as f:
            json.dump(self.results, f, indent=2)

        # Also save summary as CSV
        summary_data = []
        for result in self.results:
            summary_data.append({
                'date': result['date'],
                'action': result['trading_decision']['action'],
                'strategy': result['trading_decision']['strategy'],
                'dte': result['trading_decision']['dte'],
                'confidence': result['trading_decision']['confidence'],
                'approved': result['final_decision'].get('approved', False),
                'tech_rec': result['analyst_reports']['technical']['recommendation'],
                'econ_rec': result['analyst_reports']['economist']['recommendation']
            })

        pd.DataFrame(summary_data).to_csv(
            f"tradingagents_v3_summary_{self.start_date.strftime('%Y%m%d')}_{self.end_date.strftime('%Y%m%d')}.csv",
            index=False
        )

        logging.info(f"Results saved to {filename}")


# --- MAIN EXECUTION BLOCK ---

if __name__ == "__main__":
    # Configuration
    BACKTEST_START_DATE = "2024-01-01"
    BACKTEST_END_DATE = "2024-01-05"  # Short test period

    logging.info(f"Starting TradingAgents Framework v3.0")
    logging.info(f"Backtest period: {BACKTEST_START_DATE} to {BACKTEST_END_DATE}")

    # Run enhanced backtest
    engine = EnhancedBacktestEngine(
        start_date=BACKTEST_START_DATE,
        end_date=BACKTEST_END_DATE
    )

    engine.run_backtest()

    logging.info("TradingAgents simulation complete")